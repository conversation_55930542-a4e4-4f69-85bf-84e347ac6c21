"""
Range Duration Analyzer for Range Break Index
Analyzes how long ticks spend inside ranges before breakouts
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class RangeDurationAnalyzer:
    """
    Analyzes the duration of range-bound periods in Range Break Index tick data
    """
    
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.ranges = []
        self.range_durations = []
        
    def load_and_prepare_data(self):
        """Load and prepare tick data"""
        print("Loading Range Break Index tick data...")
        
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"Loaded {len(self.df)} tick records")
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
            
        # Convert time to datetime and sort
        self.df['time'] = pd.to_datetime(self.df['time'])
        self.df = self.df.sort_values('time').reset_index(drop=True)
        
        # Calculate price changes
        self.df['price_change'] = self.df['bid'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['bid'].shift(1)) * 100
        
        print(f"Data prepared. Time range: {self.df['time'].min()} to {self.df['time'].max()}")
        print(f"Price range: {self.df['bid'].min():.1f} - {self.df['bid'].max():.1f}")
        
        return True
    
    def identify_ranges(self, range_threshold=10.0, min_duration_minutes=1):
        """
        Identify range-bound periods in the data
        
        Parameters:
        - range_threshold: Maximum price range to consider as "range-bound"
        - min_duration_minutes: Minimum duration to qualify as a range
        """
        print(f"Identifying ranges (threshold: {range_threshold} points, min duration: {min_duration_minutes} min)...")
        
        ranges = []
        current_range = None
        
        # Use rolling window to identify range periods
        window_size = 100  # Number of ticks to analyze
        
        for i in range(window_size, len(self.df)):
            # Get window of recent ticks
            window = self.df.iloc[i-window_size:i]
            
            # Calculate range in this window
            price_range = window['bid'].max() - window['bid'].min()
            window_duration = (window['time'].iloc[-1] - window['time'].iloc[0]).total_seconds() / 60
            
            # Check if this qualifies as a range-bound period
            is_range_bound = (price_range <= range_threshold and 
                            window_duration >= min_duration_minutes)
            
            if is_range_bound:
                if current_range is None:
                    # Start new range
                    current_range = {
                        'start_idx': i - window_size,
                        'start_time': window['time'].iloc[0],
                        'start_price': window['bid'].iloc[0],
                        'high': window['bid'].max(),
                        'low': window['bid'].min(),
                        'range_size': price_range
                    }
                else:
                    # Extend current range
                    current_range['high'] = max(current_range['high'], window['bid'].max())
                    current_range['low'] = min(current_range['low'], window['bid'].min())
                    current_range['range_size'] = current_range['high'] - current_range['low']
            else:
                if current_range is not None:
                    # End current range
                    current_range['end_idx'] = i
                    current_range['end_time'] = self.df.iloc[i]['time']
                    current_range['end_price'] = self.df.iloc[i]['bid']
                    current_range['duration_minutes'] = (
                        current_range['end_time'] - current_range['start_time']
                    ).total_seconds() / 60
                    current_range['tick_count'] = current_range['end_idx'] - current_range['start_idx']
                    
                    # Only keep ranges that meet minimum duration
                    if current_range['duration_minutes'] >= min_duration_minutes:
                        ranges.append(current_range)
                    
                    current_range = None
        
        # Handle case where data ends while in a range
        if current_range is not None:
            current_range['end_idx'] = len(self.df) - 1
            current_range['end_time'] = self.df.iloc[-1]['time']
            current_range['end_price'] = self.df.iloc[-1]['bid']
            current_range['duration_minutes'] = (
                current_range['end_time'] - current_range['start_time']
            ).total_seconds() / 60
            current_range['tick_count'] = current_range['end_idx'] - current_range['start_idx']
            
            if current_range['duration_minutes'] >= min_duration_minutes:
                ranges.append(current_range)
        
        self.ranges = ranges
        print(f"Identified {len(ranges)} range-bound periods")
        
        return ranges
    
    def analyze_range_durations(self):
        """Analyze the duration statistics of identified ranges"""
        if not self.ranges:
            print("No ranges identified. Run identify_ranges() first.")
            return None
        
        print("\n" + "="*60)
        print("RANGE DURATION ANALYSIS")
        print("="*60)
        
        # Extract duration data
        durations = [r['duration_minutes'] for r in self.ranges]
        tick_counts = [r['tick_count'] for r in self.ranges]
        range_sizes = [r['range_size'] for r in self.ranges]
        
        # Calculate statistics
        stats = {
            'total_ranges': len(self.ranges),
            'avg_duration_minutes': np.mean(durations),
            'median_duration_minutes': np.median(durations),
            'min_duration_minutes': np.min(durations),
            'max_duration_minutes': np.max(durations),
            'std_duration_minutes': np.std(durations),
            'avg_tick_count': np.mean(tick_counts),
            'avg_range_size': np.mean(range_sizes)
        }
        
        print(f"📊 Range Duration Statistics:")
        print(f"   Total ranges identified: {stats['total_ranges']}")
        print(f"   Average duration: {stats['avg_duration_minutes']:.2f} minutes")
        print(f"   Median duration: {stats['median_duration_minutes']:.2f} minutes")
        print(f"   Min duration: {stats['min_duration_minutes']:.2f} minutes")
        print(f"   Max duration: {stats['max_duration_minutes']:.2f} minutes")
        print(f"   Standard deviation: {stats['std_duration_minutes']:.2f} minutes")
        print(f"   Average ticks per range: {stats['avg_tick_count']:.0f}")
        print(f"   Average range size: {stats['avg_range_size']:.1f} points")
        
        # Duration distribution
        print(f"\n📈 Duration Distribution:")
        duration_bins = [0, 1, 2, 5, 10, 20, 30, 60, float('inf')]
        duration_labels = ['<1min', '1-2min', '2-5min', '5-10min', '10-20min', '20-30min', '30-60min', '>60min']
        
        for i, (start, end) in enumerate(zip(duration_bins[:-1], duration_bins[1:])):
            count = sum(1 for d in durations if start <= d < end)
            percentage = count / len(durations) * 100
            print(f"   {duration_labels[i]}: {count} ranges ({percentage:.1f}%)")
        
        return stats
    
    def analyze_breakout_patterns(self, breakout_threshold=5.0):
        """Analyze what happens after ranges end (breakouts)"""
        if not self.ranges:
            print("No ranges identified. Run identify_ranges() first.")
            return None
        
        print(f"\n📈 Breakout Analysis (threshold: {breakout_threshold} points):")
        
        breakouts = []
        
        for range_data in self.ranges:
            end_idx = range_data['end_idx']
            
            # Look at next 50 ticks after range ends
            if end_idx + 50 < len(self.df):
                post_range = self.df.iloc[end_idx:end_idx + 50]
                
                # Calculate maximum move after range
                range_end_price = range_data['end_price']
                max_up_move = post_range['bid'].max() - range_end_price
                max_down_move = range_end_price - post_range['bid'].min()
                
                # Determine breakout direction and magnitude
                if max_up_move > breakout_threshold:
                    breakout_type = 'upward'
                    breakout_magnitude = max_up_move
                elif max_down_move > breakout_threshold:
                    breakout_type = 'downward'
                    breakout_magnitude = max_down_move
                else:
                    breakout_type = 'none'
                    breakout_magnitude = max(max_up_move, max_down_move)
                
                breakouts.append({
                    'range_duration': range_data['duration_minutes'],
                    'range_size': range_data['range_size'],
                    'breakout_type': breakout_type,
                    'breakout_magnitude': breakout_magnitude
                })
        
        # Analyze breakout statistics
        total_breakouts = len(breakouts)
        upward_breakouts = sum(1 for b in breakouts if b['breakout_type'] == 'upward')
        downward_breakouts = sum(1 for b in breakouts if b['breakout_type'] == 'downward')
        no_breakouts = sum(1 for b in breakouts if b['breakout_type'] == 'none')
        
        print(f"   Total ranges analyzed: {total_breakouts}")
        print(f"   Upward breakouts: {upward_breakouts} ({upward_breakouts/total_breakouts*100:.1f}%)")
        print(f"   Downward breakouts: {downward_breakouts} ({downward_breakouts/total_breakouts*100:.1f}%)")
        print(f"   No significant breakout: {no_breakouts} ({no_breakouts/total_breakouts*100:.1f}%)")
        
        # Average breakout magnitude
        if upward_breakouts > 0:
            avg_up_magnitude = np.mean([b['breakout_magnitude'] for b in breakouts if b['breakout_type'] == 'upward'])
            print(f"   Average upward breakout: {avg_up_magnitude:.1f} points")
        
        if downward_breakouts > 0:
            avg_down_magnitude = np.mean([b['breakout_magnitude'] for b in breakouts if b['breakout_type'] == 'downward'])
            print(f"   Average downward breakout: {avg_down_magnitude:.1f} points")
        
        return breakouts

    def visualize_range_analysis(self):
        """Create visualizations of range duration analysis"""
        if not self.ranges:
            print("No ranges to visualize. Run identify_ranges() first.")
            return

        # Extract data for plotting
        durations = [r['duration_minutes'] for r in self.ranges]
        range_sizes = [r['range_size'] for r in self.ranges]
        tick_counts = [r['tick_count'] for r in self.ranges]

        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. Duration histogram
        axes[0,0].hist(durations, bins=30, alpha=0.7, edgecolor='black')
        axes[0,0].set_title('Range Duration Distribution')
        axes[0,0].set_xlabel('Duration (minutes)')
        axes[0,0].set_ylabel('Frequency')
        axes[0,0].axvline(np.mean(durations), color='red', linestyle='--',
                         label=f'Mean: {np.mean(durations):.1f} min')
        axes[0,0].legend()

        # 2. Range size vs duration scatter
        axes[0,1].scatter(durations, range_sizes, alpha=0.6)
        axes[0,1].set_title('Range Size vs Duration')
        axes[0,1].set_xlabel('Duration (minutes)')
        axes[0,1].set_ylabel('Range Size (points)')

        # Add correlation
        correlation = np.corrcoef(durations, range_sizes)[0,1]
        axes[0,1].text(0.05, 0.95, f'Correlation: {correlation:.3f}',
                      transform=axes[0,1].transAxes, verticalalignment='top')

        # 3. Tick count vs duration
        axes[1,0].scatter(durations, tick_counts, alpha=0.6, color='green')
        axes[1,0].set_title('Tick Count vs Duration')
        axes[1,0].set_xlabel('Duration (minutes)')
        axes[1,0].set_ylabel('Number of Ticks')

        # 4. Duration box plot by time of day
        # Group ranges by hour
        hours = [r['start_time'].hour for r in self.ranges]
        duration_by_hour = {}
        for hour, duration in zip(hours, durations):
            if hour not in duration_by_hour:
                duration_by_hour[hour] = []
            duration_by_hour[hour].append(duration)

        if len(duration_by_hour) > 1:
            hours_sorted = sorted(duration_by_hour.keys())
            duration_data = [duration_by_hour[h] for h in hours_sorted]
            axes[1,1].boxplot(duration_data, labels=hours_sorted)
            axes[1,1].set_title('Range Duration by Hour of Day')
            axes[1,1].set_xlabel('Hour')
            axes[1,1].set_ylabel('Duration (minutes)')
        else:
            axes[1,1].text(0.5, 0.5, 'Insufficient data\nfor hourly analysis',
                          ha='center', va='center', transform=axes[1,1].transAxes)
            axes[1,1].set_title('Range Duration by Hour of Day')

        plt.tight_layout()
        plt.savefig('range_duration_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 Visualization saved as 'range_duration_analysis.png'")

    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        if not self.ranges:
            print("No data to report. Run analysis first.")
            return

        print("\n" + "="*60)
        print("COMPREHENSIVE RANGE DURATION REPORT")
        print("="*60)

        # Basic statistics
        durations = [r['duration_minutes'] for r in self.ranges]

        print(f"\n📊 Key Findings:")
        print(f"   • Total range periods identified: {len(self.ranges)}")
        print(f"   • Average time in range: {np.mean(durations):.2f} minutes")
        print(f"   • Median time in range: {np.median(durations):.2f} minutes")
        print(f"   • Longest range period: {np.max(durations):.2f} minutes")
        print(f"   • Shortest range period: {np.min(durations):.2f} minutes")

        # Percentiles
        p25 = np.percentile(durations, 25)
        p75 = np.percentile(durations, 75)
        p90 = np.percentile(durations, 90)

        print(f"\n📈 Duration Percentiles:")
        print(f"   • 25th percentile: {p25:.2f} minutes")
        print(f"   • 75th percentile: {p75:.2f} minutes")
        print(f"   • 90th percentile: {p90:.2f} minutes")

        # Trading implications
        print(f"\n💡 Trading Implications:")
        print(f"   • Typical range duration: {p25:.1f} - {p75:.1f} minutes")
        print(f"   • Strategy window: Plan for {np.mean(durations):.1f} minute average holds")
        print(f"   • Risk management: Expect up to {p90:.1f} minute range periods")

        # Data coverage
        total_time = (self.df['time'].max() - self.df['time'].min()).total_seconds() / 60
        range_time = sum(durations)
        coverage = range_time / total_time * 100

        print(f"\n⏱️ Time Analysis:")
        print(f"   • Total data period: {total_time:.1f} minutes")
        print(f"   • Time spent in ranges: {range_time:.1f} minutes ({coverage:.1f}%)")
        print(f"   • Time in breakout/trending: {total_time - range_time:.1f} minutes ({100-coverage:.1f}%)")

    def run_complete_analysis(self, range_threshold=10.0, min_duration_minutes=1):
        """Run complete range duration analysis"""
        print("🔬 Starting Range Duration Analysis for Range Break Index...")

        # Load data
        if not self.load_and_prepare_data():
            return

        # Identify ranges
        self.identify_ranges(range_threshold, min_duration_minutes)

        # Analyze durations
        duration_stats = self.analyze_range_durations()

        # Analyze breakouts
        breakout_stats = self.analyze_breakout_patterns()

        # Generate visualizations
        self.visualize_range_analysis()

        # Generate summary report
        self.generate_summary_report()

        print(f"\n✅ Analysis complete!")

        return {
            'ranges': self.ranges,
            'duration_stats': duration_stats,
            'breakout_stats': breakout_stats
        }

def main():
    """Main execution function"""
    data_file = "Range_Break_100_7days_20250623_20250630.csv"

    # Create analyzer
    analyzer = RangeDurationAnalyzer(data_file)

    # Run complete analysis
    results = analyzer.run_complete_analysis(
        range_threshold=10.0,  # 10 point range threshold
        min_duration_minutes=1  # Minimum 1 minute duration
    )

    return results

if __name__ == "__main__":
    analysis_results = main()
