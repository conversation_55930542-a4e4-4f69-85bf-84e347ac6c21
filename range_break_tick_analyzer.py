import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class RangeBreakTickAnalyzer:
    """
    Intelligent analyzer for Range Break Index tick data to identify range ceiling patterns before spikes
    """
    
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.df = None
        self.spikes = []
        self.range_ceilings = []
        self.patterns = {}
        
    def load_and_prepare_data(self):
        """Load and prepare tick data for analysis"""
        print("Loading Range Break Index tick data...")
        
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"Loaded {len(self.df)} tick records")
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
            
        # Convert time to datetime and sort
        self.df['time'] = pd.to_datetime(self.df['time'])
        self.df = self.df.sort_values('time').reset_index(drop=True)
        
        # Calculate price changes and movements
        self.df['price_change'] = self.df['bid'].diff()
        self.df['price_change_pct'] = (self.df['price_change'] / self.df['bid'].shift(1)) * 100
        self.df['abs_change'] = self.df['price_change'].abs()
        
        # Add time-based features
        self.df['seconds'] = (self.df['time'] - self.df['time'].iloc[0]).dt.total_seconds()
        self.df['tick_interval'] = self.df['seconds'].diff()
        
        print(f"Data prepared. Price range: {self.df['bid'].min():.1f} - {self.df['bid'].max():.1f}")
        return True
    
    def detect_spikes(self, spike_threshold_pct=0.5, min_spike_size=5.0):
        """Detect significant price spikes in the data"""
        print(f"Detecting spikes (threshold: {spike_threshold_pct}% or {min_spike_size} points)...")
        
        # Define spikes based on percentage change OR absolute change
        spike_condition = (
            (self.df['price_change_pct'].abs() > spike_threshold_pct) |
            (self.df['abs_change'] > min_spike_size)
        )
        
        spike_indices = self.df[spike_condition].index.tolist()
        
        # Store spike information
        self.spikes = []
        for idx in spike_indices:
            spike_info = {
                'index': idx,
                'time': self.df.loc[idx, 'time'],
                'price': self.df.loc[idx, 'bid'],
                'change': self.df.loc[idx, 'price_change'],
                'change_pct': self.df.loc[idx, 'price_change_pct'],
                'direction': 'up' if self.df.loc[idx, 'price_change'] > 0 else 'down'
            }
            self.spikes.append(spike_info)
        
        print(f"Detected {len(self.spikes)} spikes")
        
        # Spike statistics
        if self.spikes:
            changes = [s['change'] for s in self.spikes]
            print(f"Spike size range: {min(changes):.1f} to {max(changes):.1f}")
            print(f"Average spike size: {np.mean([abs(c) for c in changes]):.1f}")
        
        return self.spikes
    
    def analyze_range_ceilings_before_spikes(self, lookback_ticks=100, ceiling_tolerance=0.5):
        """Analyze range ceiling patterns before spikes"""
        print(f"Analyzing range ceilings before spikes (lookback: {lookback_ticks} ticks)...")
        
        ceiling_patterns = []
        
        for spike in self.spikes:
            spike_idx = spike['index']
            
            # Skip if not enough data before spike
            if spike_idx < lookback_ticks:
                continue
                
            # Get data before spike
            pre_spike_data = self.df.iloc[spike_idx - lookback_ticks:spike_idx].copy()
            
            if len(pre_spike_data) < 10:  # Need minimum data
                continue
            
            # Find potential ceiling levels (local highs)
            pre_spike_data['is_local_high'] = (
                (pre_spike_data['bid'] >= pre_spike_data['bid'].shift(1)) &
                (pre_spike_data['bid'] >= pre_spike_data['bid'].shift(-1))
            )
            
            local_highs = pre_spike_data[pre_spike_data['is_local_high']]['bid'].values
            
            if len(local_highs) < 2:
                continue
            
            # Group similar price levels (within tolerance)
            ceiling_levels = []
            for price in local_highs:
                # Check if this price is close to an existing ceiling level
                found_ceiling = False
                for ceiling in ceiling_levels:
                    if abs(price - ceiling['price']) <= ceiling_tolerance:
                        ceiling['touches'] += 1
                        ceiling['prices'].append(price)
                        found_ceiling = True
                        break
                
                if not found_ceiling:
                    ceiling_levels.append({
                        'price': price,
                        'touches': 1,
                        'prices': [price]
                    })
            
            # Find the most significant ceiling (most touches)
            if ceiling_levels:
                main_ceiling = max(ceiling_levels, key=lambda x: x['touches'])
                
                if main_ceiling['touches'] >= 3:  # Minimum 3 touches to be considered a ceiling
                    # Calculate time spent at ceiling
                    ceiling_price = np.mean(main_ceiling['prices'])
                    ceiling_touches = pre_spike_data[
                        abs(pre_spike_data['bid'] - ceiling_price) <= ceiling_tolerance
                    ]
                    
                    time_at_ceiling = len(ceiling_touches)
                    
                    pattern = {
                        'spike_index': spike_idx,
                        'spike_time': spike['time'],
                        'spike_change': spike['change'],
                        'spike_direction': spike['direction'],
                        'ceiling_price': ceiling_price,
                        'ceiling_touches': main_ceiling['touches'],
                        'time_at_ceiling': time_at_ceiling,
                        'pre_spike_range': pre_spike_data['bid'].max() - pre_spike_data['bid'].min(),
                        'distance_to_ceiling': abs(spike['price'] - ceiling_price)
                    }
                    
                    ceiling_patterns.append(pattern)
        
        self.range_ceilings = ceiling_patterns
        print(f"Found {len(ceiling_patterns)} range ceiling patterns before spikes")
        
        return ceiling_patterns
    
    def analyze_consolidation_patterns(self, lookback_ticks=50, consolidation_threshold=2.0):
        """Analyze price consolidation patterns before spikes"""
        print(f"Analyzing consolidation patterns before spikes...")
        
        consolidation_patterns = []
        
        for spike in self.spikes:
            spike_idx = spike['index']
            
            if spike_idx < lookback_ticks:
                continue
                
            # Get data before spike
            pre_spike_data = self.df.iloc[spike_idx - lookback_ticks:spike_idx].copy()
            
            # Calculate rolling range (high-low over windows)
            window_sizes = [10, 20, 30]
            
            for window in window_sizes:
                if len(pre_spike_data) >= window:
                    rolling_high = pre_spike_data['bid'].rolling(window=window).max()
                    rolling_low = pre_spike_data['bid'].rolling(window=window).min()
                    rolling_range = rolling_high - rolling_low
                    
                    # Find periods of tight consolidation
                    tight_periods = rolling_range <= consolidation_threshold
                    
                    if tight_periods.any():
                        # Find the longest consolidation period
                        consolidation_length = 0
                        current_length = 0
                        
                        for is_tight in tight_periods:
                            if is_tight:
                                current_length += 1
                                consolidation_length = max(consolidation_length, current_length)
                            else:
                                current_length = 0
                        
                        if consolidation_length >= 5:  # Minimum 5 ticks of consolidation
                            pattern = {
                                'spike_index': spike_idx,
                                'window_size': window,
                                'consolidation_length': consolidation_length,
                                'avg_range_during_consolidation': rolling_range[tight_periods].mean(),
                                'spike_size': abs(spike['change']),
                                'breakout_direction': spike['direction']
                            }
                            consolidation_patterns.append(pattern)
        
        print(f"Found {len(consolidation_patterns)} consolidation patterns")
        return consolidation_patterns
    
    def analyze_tick_frequency_patterns(self, lookback_seconds=300):
        """Analyze tick frequency changes before spikes"""
        print("Analyzing tick frequency patterns before spikes...")
        
        frequency_patterns = []
        
        for spike in self.spikes:
            spike_idx = spike['index']
            spike_time = spike['time']
            
            # Get data in time window before spike
            time_cutoff = spike_time - timedelta(seconds=lookback_seconds)
            pre_spike_mask = (self.df['time'] >= time_cutoff) & (self.df['time'] < spike_time)
            pre_spike_data = self.df[pre_spike_mask].copy()
            
            if len(pre_spike_data) < 10:
                continue
            
            # Calculate tick frequency (ticks per minute)
            pre_spike_data['minute'] = pre_spike_data['time'].dt.floor('1min')
            ticks_per_minute = pre_spike_data.groupby('minute').size()
            
            if len(ticks_per_minute) >= 2:
                pattern = {
                    'spike_index': spike_idx,
                    'avg_ticks_per_minute': ticks_per_minute.mean(),
                    'max_ticks_per_minute': ticks_per_minute.max(),
                    'min_ticks_per_minute': ticks_per_minute.min(),
                    'tick_frequency_variance': ticks_per_minute.var(),
                    'spike_size': abs(spike['change'])
                }
                frequency_patterns.append(pattern)
        
        print(f"Analyzed tick frequency for {len(frequency_patterns)} spikes")
        return frequency_patterns

    def generate_pattern_statistics(self):
        """Generate comprehensive statistics about identified patterns"""
        print("\n" + "="*60)
        print("RANGE BREAK INDEX PATTERN ANALYSIS")
        print("="*60)

        if not self.range_ceilings:
            print("No range ceiling patterns found. Run analyze_range_ceilings_before_spikes() first.")
            return

        # Range ceiling statistics
        print(f"\n--- RANGE CEILING PATTERNS ---")
        print(f"Total patterns found: {len(self.range_ceilings)}")

        if self.range_ceilings:
            touches = [p['ceiling_touches'] for p in self.range_ceilings]
            time_at_ceiling = [p['time_at_ceiling'] for p in self.range_ceilings]
            spike_sizes = [abs(p['spike_change']) for p in self.range_ceilings]

            print(f"Average ceiling touches: {np.mean(touches):.1f}")
            print(f"Max ceiling touches: {max(touches)}")
            print(f"Average time at ceiling: {np.mean(time_at_ceiling):.1f} ticks")
            print(f"Average spike size after ceiling: {np.mean(spike_sizes):.1f}")

            # Direction analysis
            directions = [p['spike_direction'] for p in self.range_ceilings]
            up_spikes = sum(1 for d in directions if d == 'up')
            down_spikes = sum(1 for d in directions if d == 'down')

            print(f"Upward spikes after ceiling: {up_spikes} ({up_spikes/len(directions)*100:.1f}%)")
            print(f"Downward spikes after ceiling: {down_spikes} ({down_spikes/len(directions)*100:.1f}%)")

    def visualize_spike_patterns(self, num_examples=5):
        """Visualize examples of range ceiling patterns before spikes"""
        if not self.range_ceilings:
            print("No patterns to visualize. Run analysis first.")
            return

        # Select top patterns (most ceiling touches)
        top_patterns = sorted(self.range_ceilings, key=lambda x: x['ceiling_touches'], reverse=True)[:num_examples]

        fig, axes = plt.subplots(num_examples, 1, figsize=(15, 4*num_examples))
        if num_examples == 1:
            axes = [axes]

        for i, pattern in enumerate(top_patterns):
            spike_idx = pattern['spike_index']
            lookback = 100

            # Get data around spike
            start_idx = max(0, spike_idx - lookback)
            end_idx = min(len(self.df), spike_idx + 20)

            plot_data = self.df.iloc[start_idx:end_idx].copy()
            plot_data['relative_idx'] = range(len(plot_data))

            # Plot price
            axes[i].plot(plot_data['relative_idx'], plot_data['bid'], 'b-', linewidth=1, alpha=0.7)

            # Mark spike point
            spike_relative_idx = spike_idx - start_idx
            axes[i].axvline(x=spike_relative_idx, color='red', linestyle='--', alpha=0.8, label='Spike')

            # Mark ceiling level
            ceiling_price = pattern['ceiling_price']
            axes[i].axhline(y=ceiling_price, color='orange', linestyle='-', alpha=0.6,
                           label=f'Ceiling ({pattern["ceiling_touches"]} touches)')

            # Highlight ceiling area
            axes[i].fill_between(plot_data['relative_idx'],
                               ceiling_price - 0.5, ceiling_price + 0.5,
                               alpha=0.2, color='orange')

            axes[i].set_title(f'Pattern {i+1}: {pattern["ceiling_touches"]} ceiling touches, '
                            f'{pattern["spike_change"]:.1f} spike')
            axes[i].set_ylabel('Price')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)

        plt.xlabel('Relative Tick Index')
        plt.tight_layout()
        plt.savefig('range_break_ceiling_patterns.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_predictive_power(self):
        """Analyze the predictive power of range ceiling patterns"""
        print("\n--- PREDICTIVE POWER ANALYSIS ---")

        if not self.range_ceilings:
            print("No patterns to analyze.")
            return

        # Analyze relationship between ceiling characteristics and spike size
        ceiling_data = pd.DataFrame(self.range_ceilings)

        if len(ceiling_data) > 0:
            # Correlation analysis
            correlations = {
                'ceiling_touches_vs_spike_size': ceiling_data['ceiling_touches'].corr(ceiling_data['spike_change'].abs()),
                'time_at_ceiling_vs_spike_size': ceiling_data['time_at_ceiling'].corr(ceiling_data['spike_change'].abs()),
                'pre_spike_range_vs_spike_size': ceiling_data['pre_spike_range'].corr(ceiling_data['spike_change'].abs())
            }

            print("Correlations with spike size:")
            for key, value in correlations.items():
                print(f"  {key}: {value:.3f}")

            # Pattern reliability
            total_spikes = len(self.spikes)
            spikes_with_ceiling = len(self.range_ceilings)

            print(f"\nPattern Reliability:")
            print(f"Total spikes: {total_spikes}")
            print(f"Spikes with ceiling pattern: {spikes_with_ceiling}")
            print(f"Ceiling pattern occurrence rate: {spikes_with_ceiling/total_spikes*100:.1f}%")

            # Ceiling touch distribution
            touch_counts = ceiling_data['ceiling_touches'].value_counts().sort_index()
            print(f"\nCeiling touch distribution:")
            for touches, count in touch_counts.items():
                print(f"  {touches} touches: {count} patterns ({count/len(ceiling_data)*100:.1f}%)")

    def run_complete_analysis(self):
        """Run complete Range Break Index tick analysis"""
        print("Starting comprehensive Range Break Index tick analysis...")

        # Load and prepare data
        if not self.load_and_prepare_data():
            return

        # Detect spikes
        self.detect_spikes()

        # Analyze range ceiling patterns
        self.analyze_range_ceilings_before_spikes()

        # Analyze consolidation patterns
        consolidation_patterns = self.analyze_consolidation_patterns()

        # Analyze tick frequency patterns
        frequency_patterns = self.analyze_tick_frequency_patterns()

        # Generate statistics
        self.generate_pattern_statistics()

        # Analyze predictive power
        self.analyze_predictive_power()

        # Visualize patterns
        if self.range_ceilings:
            self.visualize_spike_patterns()

        print(f"\n" + "="*60)
        print("ANALYSIS COMPLETE")
        print("="*60)
        print("Key findings:")
        print(f"- {len(self.spikes)} spikes detected")
        print(f"- {len(self.range_ceilings)} range ceiling patterns found")
        print(f"- {len(consolidation_patterns)} consolidation patterns found")
        print(f"- Generated visualization: range_break_ceiling_patterns.png")

        return {
            'spikes': self.spikes,
            'range_ceilings': self.range_ceilings,
            'consolidation_patterns': consolidation_patterns,
            'frequency_patterns': frequency_patterns
        }

def main():
    """Main execution function"""
    data_file = "Range_Break_100_7days_20250623_20250630.csv"

    # Create analyzer
    analyzer = RangeBreakTickAnalyzer(data_file)

    # Run complete analysis
    results = analyzer.run_complete_analysis()

    return results

if __name__ == "__main__":
    analysis_results = main()
